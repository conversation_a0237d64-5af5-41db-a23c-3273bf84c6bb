import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Min } from 'class-validator';

/**
 * DTO cho việc tạo đơn hàng mua R-Point
 */
export class CreatePaymentDto {
  @ApiProperty({
    description: 'ID của gói R-Point',
    example: 1
  })
  @IsNumber()
  pointPackageId: number;

  @ApiProperty({
    description: 'Số lượng R-Point muốn mua (chỉ áp dụng cho gói customize)',
    example: 100,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Số lượng R-Point phải lớn hơn 0' })
  amount?: number;

  @ApiProperty({
    description: 'Mã khuyến mãi (nếu có)',
    example: 'SUMMER2023',
    required: false
  })
  @IsOptional()
  @IsString()
  couponCode?: string;
}
