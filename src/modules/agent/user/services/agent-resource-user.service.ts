import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentMediaRepository,
  AgentProductRepository,
  AgentRepository,
  AgentUrlRepository,
  AgentUserRepository
} from '@modules/agent/repositories';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import {
  AddAgentMediaDto,
  AddAgentProductDto,
  AddAgentUrlDto,
  AgentMediaQueryDto,
  AgentMediaResponseDto,
  AgentProductQueryDto,
  AgentProductResponseDto,
  AgentUrlQueryDto,
  AgentUrlResponseDto,
} from '../dto';

/**
 * Service xử lý các thao tác liên quan đến tài nguyên của agent cho người dùng
 */
@Injectable()
export class AgentResourceUserService {
  private readonly logger = new Logger(AgentResourceUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentUserRepository: AgentUserRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentUrlRepository: AgentUrlRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly urlRepository: UrlRepository,
    @InjectRepository(UserProduct)
    private readonly userProductRepo: Repository<UserProduct>,
  ) { }

  /**
   * Lấy danh sách media của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách media có phân trang
   */
  async getAgentMedia(
    agentId: string,
    userId: number,
    queryDto: AgentMediaQueryDto,
  ): Promise<PaginatedResult<AgentMediaResponseDto>> {
    try {
      // Kiểm tra agent có tồn tại không
      await this.checkAgentExists(agentId, userId);

      // Sử dụng repository để lấy danh sách media của agent
      const result = await this.agentMediaRepository.findPaginated(agentId, queryDto);

      // Chuyển đổi kết quả sang DTO
      const mediaItems = await Promise.all(result.items.map(async (item) => {
        // Lấy thông tin media từ repository - chỉ select các trường cần thiết
        const media = await this.mediaRepository.findOne({
          where: { id: item.mediaId },
          select: ['id', 'name', 'storageKey', 'createdAt']
        });

        if (!media) {
          throw new AppException(AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
        }

        return {
          id: item.mediaId,
          name: media.name,
          url: media.storageKey || '',
          createdAt: media.createdAt,
        };
      }));

      return {
        items: mediaItems,
        meta: result.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách media của agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm nhiều media vào agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin thêm media
   */
  @Transactional()
  async addAgentMedias(
    agentId: string,
    userId: number,
    addDto: AddAgentMediaDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không
      await this.checkAgentExists(agentId, userId);

      // Thêm từng media vào agent
      for (const mediaId of addDto.mediaIds) {
        // Kiểm tra media có tồn tại không và người dùng có quyền truy cập không
        const media = await this.mediaRepository.findOne({
          where: { id: mediaId, ownedBy: userId }
        });

        if (!media) {
          throw new AppException(AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
        }

        // Sử dụng repository để thêm media vào agent
        await this.agentMediaRepository.addMedia(agentId, mediaId);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi thêm media vào agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Xóa media khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param mediaId ID của media
   */
  @Transactional()
  async removeAgentMedia(
    agentId: string,
    userId: number,
    mediaId: string,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không
      await this.checkAgentExists(agentId, userId);

      // Kiểm tra media có tồn tại không và người dùng có quyền truy cập không
      const media = await this.mediaRepository.findOne({
        where: { id: mediaId }
      });

      if (!media) {
        throw new AppException(AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập media
      if (media.ownedBy !== userId) {
        throw new AppException(AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
      }

      // Sử dụng repository để xóa media khỏi agent
      await this.agentMediaRepository.removeMedia(agentId, mediaId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa media khỏi agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Lấy danh sách URL của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách URL có phân trang
   */
  async getAgentUrl(
    agentId: string,
    userId: number,
    queryDto: AgentUrlQueryDto,
  ): Promise<PaginatedResult<AgentUrlResponseDto>> {
    try {
      // Kiểm tra agent có tồn tại không
      await this.checkAgentExists(agentId, userId);

      // Sử dụng repository để lấy danh sách URL của agent
      const result = await this.agentUrlRepository.findPaginated(agentId, queryDto);

      // Chuyển đổi kết quả sang DTO
      const urlItems = await Promise.all(result.items.map(async (item) => {
        // Lấy thông tin URL từ repository - chỉ select các trường cần thiết
        const url = await this.urlRepository.findOne({
          where: { id: item.urlId },
          select: ['id', 'url', 'title', 'createdAt']
        });

        if (!url) {
          throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
        }

        return {
          id: item.urlId,
          url: url.url,
          title: url.title,
          createdAt: url.createdAt,
        };
      }));

      return {
        items: urlItems,
        meta: result.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách URL của agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm nhiều URL vào agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin thêm URL
   */
  @Transactional()
  async addAgentUrls(
    agentId: string,
    userId: number,
    addDto: AddAgentUrlDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không
      await this.checkAgentExists(agentId, userId);

      // Thêm từng URL vào agent
      for (const urlId of addDto.urlIds) {
        // Check if URL exists and user has access
        const url = await this.urlRepository.findOne({
          where: { id: urlId }
        });

        if (!url) {
          throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
        }

        // Check URL access permission
        if (url.ownedBy !== userId) {
          throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
        }

        // Sử dụng repository để thêm URL vào agent
        await this.agentUrlRepository.addUrl(agentId, urlId);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi thêm URL vào agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Xóa URL khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param urlId ID của URL
   */
  @Transactional()
  async removeAgentUrl(
    agentId: string,
    userId: number,
    urlId: string,
  ): Promise<void> {
    try {
      // Check if agent exists
      await this.checkAgentExists(agentId, userId);

      // Check if URL exists and user has access
      const url = await this.urlRepository.findOne({
        where: { id: urlId }
      });

      if (!url) {
        throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
      }

      // Check URL access permission
      if (url.ownedBy !== userId) {
        throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
      }

      // Use repository to remove URL from agent
      await this.agentUrlRepository.removeUrl(agentId, urlId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa URL khỏi agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Get agent products
   * @param agentId Agent ID
   * @param userId User ID
   * @param queryDto Query parameters
   * @returns Paginated product list
   */
  async getAgentProduct(
    agentId: string,
    userId: number,
    queryDto: AgentProductQueryDto,
  ): Promise<PaginatedResult<AgentProductResponseDto>> {
    try {
      // Check if agent exists
      await this.checkAgentExists(agentId, userId);

      // Use repository to get agent products
      const result = await this.agentProductRepository.findPaginated(agentId, queryDto);

      // Convert results to DTO
      const productItems = await Promise.all(result.items.map(async (item) => {
        // Get product information from repository - only select necessary fields
        const product = await this.userProductRepo.findOne({
          where: { id: parseInt(item.productId.toString()) },
          select: ['id', 'name', 'images', 'createdAt']
        });

        if (!product) {
          throw new AppException(AGENT_ERROR_CODES.PRODUCT_NOT_FOUND);
        }

        return {
          id: item.productId.toString(),
          name: product.name,
          imageUrl: product.images ? product.images[0] : '',
          createdAt: product.createdAt,
        };
      }));

      return {
        items: productItems,
        meta: result.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting agent products: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Add multiple products to agent
   * @param agentId Agent ID
   * @param userId User ID
   * @param addDto Product information to add
   */
  @Transactional()
  async addAgentProducts(
    agentId: string,
    userId: number,
    addDto: AddAgentProductDto,
  ): Promise<void> {
    try {
      // Check if agent exists
      await this.checkAgentExists(agentId, userId);

      // Add each product to agent
      for (const productId of addDto.productIds) {
        // Check if product exists
        const product = await this.userProductRepo.findOne({
          where: { id: parseInt(productId) }
        });

        if (!product) {
          throw new AppException(AGENT_ERROR_CODES.PRODUCT_NOT_FOUND);
        }

        // Use repository to add product to agent
        await this.agentProductRepository.addProduct(agentId, productId);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error adding products to agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Remove product from agent
   * @param agentId Agent ID
   * @param userId User ID
   * @param productId Product ID
   */
  @Transactional()
  async removeAgentProduct(
    agentId: string,
    userId: number,
    productId: string,
  ): Promise<void> {
    try {
      // Check if agent exists
      await this.checkAgentExists(agentId, userId);

      // Check if product exists
      const product = await this.userProductRepo.findOne({
        where: { id: parseInt(productId) }
      });

      if (!product) {
        throw new AppException(AGENT_ERROR_CODES.PRODUCT_NOT_FOUND);
      }

      // Use repository to remove product from agent
      await this.agentProductRepository.removeProduct(agentId, productId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing product from agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật danh sách media của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   */
  @Transactional()
  async updateAgentMedia(
    agentId: string,
    userId: number,
    updateDto: AddAgentMediaDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không
      await this.checkAgentExists(agentId, userId);

      // Lấy danh sách media hiện tại của agent
      const currentMedias = await this.agentMediaRepository.findByAgentId(agentId);
      const currentMediaIds = currentMedias.map(media => media.mediaId);

      // Xác định media cần thêm và cần xóa
      const mediaIdsToAdd = updateDto.mediaIds.filter(id => !currentMediaIds.includes(id));
      const mediaIdsToRemove = currentMediaIds.filter(id => !updateDto.mediaIds.includes(id));

      // Xóa các media không còn trong danh sách
      for (const mediaId of mediaIdsToRemove) {
        await this.agentMediaRepository.removeMedia(agentId, mediaId);
      }

      // Thêm các media mới
      for (const mediaId of mediaIdsToAdd) {
        // Kiểm tra media có tồn tại không và người dùng có quyền truy cập không
        const media = await this.mediaRepository.findOne({
          where: { id: mediaId, ownedBy: userId }
        });

        if (!media) {
          throw new AppException(AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
        }

        // Sử dụng repository để thêm media vào agent
        await this.agentMediaRepository.addMedia(agentId, mediaId);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật danh sách media của agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật danh sách URL của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   */
  @Transactional()
  async updateAgentUrl(
    agentId: string,
    userId: number,
    updateDto: AddAgentUrlDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không
      await this.checkAgentExists(agentId, userId);

      // Lấy danh sách URL hiện tại của agent
      const currentUrls = await this.agentUrlRepository.findByAgentId(agentId);
      const currentUrlIds = currentUrls.map(url => url.urlId);

      // Xác định URL cần thêm và cần xóa
      const urlIdsToAdd = updateDto.urlIds.filter(id => !currentUrlIds.includes(id));
      const urlIdsToRemove = currentUrlIds.filter(id => !updateDto.urlIds.includes(id));

      // Xóa các URL không còn trong danh sách
      for (const urlId of urlIdsToRemove) {
        await this.agentUrlRepository.removeUrl(agentId, urlId);
      }

      // Thêm các URL mới
      for (const urlId of urlIdsToAdd) {
        // Kiểm tra URL có tồn tại không và người dùng có quyền truy cập không
        const url = await this.urlRepository.findOne({
          where: { id: urlId }
        });

        if (!url) {
          throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
        }

        // Kiểm tra quyền truy cập URL
        if (url.ownedBy !== userId) {
          throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
        }

        // Sử dụng repository để thêm URL vào agent
        await this.agentUrlRepository.addUrl(agentId, urlId);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật danh sách URL của agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật danh sách sản phẩm của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   */
  @Transactional()
  async updateAgentProduct(
    agentId: string,
    userId: number,
    updateDto: AddAgentProductDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không
      await this.checkAgentExists(agentId, userId);

      // Lấy danh sách sản phẩm hiện tại của agent
      const currentProducts = await this.agentProductRepository.findByAgentId(agentId);
      const currentProductIds = currentProducts.map(product => product.productId.toString());

      // Xác định sản phẩm cần thêm và cần xóa
      const productIdsToAdd = updateDto.productIds.filter(id => !currentProductIds.includes(id));
      const productIdsToRemove = currentProductIds.filter(id => !updateDto.productIds.includes(id));

      // Xóa các sản phẩm không còn trong danh sách
      for (const productId of productIdsToRemove) {
        await this.agentProductRepository.removeProduct(agentId, productId);
      }

      // Thêm các sản phẩm mới
      for (const productId of productIdsToAdd) {
        // Kiểm tra sản phẩm có tồn tại không
        const product = await this.userProductRepo.findOne({
          where: { id: parseInt(productId) }
        });

        if (!product) {
          throw new AppException(AGENT_ERROR_CODES.PRODUCT_NOT_FOUND);
        }

        // Sử dụng repository để thêm sản phẩm vào agent
        await this.agentProductRepository.addProduct(agentId, productId);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật danh sách sản phẩm của agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Check if agent exists and user has access
   * @param agentId Agent ID
   * @param userId User ID
   */
  private async checkAgentExists(agentId: string, userId: number): Promise<void> {
    // Kiểm tra agent có tồn tại không - chỉ select các trường cần thiết
    const agent = await this.agentRepository.createBaseQuery()
      .select(['agent.id'])
      .where('agent.id = :id', { id: agentId })
      .andWhere('agent.deletedAt IS NULL')
      .getOne();

    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Kiểm tra người dùng có quyền truy cập agent không
    const agentUser = await this.agentUserRepository.createBaseQuery()
      .select(['agentUser.id'])
      .where('agentUser.id = :id', { id: agentId })
      .andWhere('agentUser.userId = :userId', { userId })
      .getOne();

    if (!agentUser) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }
  }
}
