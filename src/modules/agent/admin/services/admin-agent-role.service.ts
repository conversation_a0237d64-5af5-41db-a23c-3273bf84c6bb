import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { AgentRoleRepository } from '@modules/agent/repositories/agent-role.repository';
import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { CdnService } from '@shared/services/cdn.service';
import {
  CreateRoleDto,
  RoleListResponseDto,
  RoleQueryDto,
  RoleResponseDto,
  RoleAgentUseDto,
  RoleTrashItemDto,
  UpdateRoleDto,
} from '../dto/agent-role';

/**
 * Service xử lý logic nghiệp vụ liên quan đến vai trò của agent
 */
@Injectable()
export class AdminAgentRoleService {
  private readonly logger = new Logger(AdminAgentRoleService.name);

  constructor(
    private readonly agentRoleRepository: AgentRoleRepository,
    private readonly agentSystemRepository: AgentSystemRepository,
    private readonly agentRepository: AgentRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly cdnService: CdnService,
  ) { }

  /**
   * Lấy danh sách vai trò với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách vai trò với phân trang
   */
  async getRoles(
    queryDto: RoleQueryDto,
  ): Promise<PaginatedResult<RoleListResponseDto>> {
    try {
      // Gọi phương thức từ repository để lấy danh sách vai trò với phân trang
      return await this.agentRoleRepository.findPaginatedRoles(queryDto);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting roles: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
    }
  }

  /**
   * Tạo vai trò mới
   * @param createRoleDto Dữ liệu tạo vai trò
   * @param employeeId ID của nhân viên thực hiện hành động
   * @returns ID của vai trò đã được tạo
   */
  @Transactional()
  async createRole(
    createRoleDto: CreateRoleDto,
    employeeId: number,
  ): Promise<string> {
    try {
      // Kiểm tra tên vai trò đã tồn tại chưa
      const existingRole = await this.agentRoleRepository.findByName(
        createRoleDto.name,
      );
      if (existingRole) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NAME_EXISTS);
      }

      // Tạo vai trò mới
      const role = await this.agentRoleRepository.createRole({
        name: createRoleDto.name,
        description: createRoleDto.description,
        createdBy: employeeId,
        updatedBy: employeeId,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        moduleMcpConfig: createRoleDto.moduleMcpConfig || {
          mcpNameServer: '',
          mcpPort: 0,
          url: '',
          useNodeEventSource: false,
          header: {},
          reconnect: {
            enable: false,
            delayMs: 1000,
            maxAttempts: 3
          }
        },
      });

      return role.id;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi tạo vai trò: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
    }
  }

  /**
   * Cập nhật vai trò
   * @param id ID của vai trò
   * @param updateRoleDto Dữ liệu cập nhật vai trò
   * @param employeeId ID của nhân viên thực hiện hành động
   */
  @Transactional()
  async updateRole(
    id: string,
    updateRoleDto: UpdateRoleDto,
    employeeId: number,
  ): Promise<void> {
    try {
      // Kiểm tra vai trò có tồn tại không
      const role = await this.agentRoleRepository.findById(id);
      if (!role) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
      }

      // Kiểm tra tên vai trò đã tồn tại chưa (nếu có cập nhật tên)
      if (updateRoleDto.name && updateRoleDto.name !== role.name) {
        const existingRole = await this.agentRoleRepository.findByName(
          updateRoleDto.name,
        );
        if (existingRole && existingRole.id !== id) {
          throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NAME_EXISTS);
        }
      }

      // Cập nhật vai trò
      await this.agentRoleRepository.updateRole(id, {
        name: updateRoleDto.name,
        description: updateRoleDto.description,
        updatedBy: employeeId,
        updatedAt: Date.now(),
        moduleMcpConfig: updateRoleDto.moduleMcpConfig,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật vai trò: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa vai trò
   * @param id ID của vai trò
   * @param employeeId ID của nhân viên thực hiện hành động
   */
  @Transactional()
  async deleteRole(id: string, employeeId: number): Promise<void> {
    try {
      // Kiểm tra vai trò có tồn tại không
      const role = await this.agentRoleRepository.findById(id);
      if (!role) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
      }

      // Kiểm tra vai trò có đang được sử dụng bởi agent system nào không
      const isRoleInUse = await this.agentRoleRepository.isRoleInUse(id, this.agentSystemRepository);
      if (isRoleInUse) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_ROLE_ALREADY_ASSIGNED,
          `Vai trò đang được sử dụng bởi agent system`,
        );
      }

      // Xóa mềm vai trò
      await this.agentRoleRepository.softDeleteAndUpdate(id, employeeId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xóa vai trò: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Lấy chi tiết vai trò theo ID
   * @param id ID của vai trò
   * @returns Chi tiết vai trò
   */
  async getRoleById(id: string): Promise<RoleResponseDto> {
    try {
      // Tìm vai trò theo ID
      const role = await this.agentRoleRepository.findById(id);

      if (!role || role.deletedAt) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
      }

      // Lấy thông tin nhân viên tạo và cập nhật
      const employeeIds: number[] = [];
      if (role.createdBy) employeeIds.push(role.createdBy);
      if (role.updatedBy) employeeIds.push(role.updatedBy);
      if (role.deletedBy) employeeIds.push(role.deletedBy);

      const employeeInfoMap =
        await this.employeeInfoService.getEmployeeInfoMap(employeeIds);

      // Parse moduleMcpConfig sử dụng phương thức từ repository
      const moduleMcpConfig = this.agentRoleRepository.parseModuleMcpConfig(role.moduleMcpConfig);

      // Lấy thông tin agent sử dụng vai trò này (nếu có)
      const agentInfo = await this.agentRoleRepository.getAgentUsingRole(
        id,
        this.agentSystemRepository,
        this.agentRepository,
        this.cdnService
      );

      // Tạo response DTO
      const result: RoleResponseDto = {
        id: role.id,
        name: role.name,
        description: role.description || '',
        moduleMcpConfig: moduleMcpConfig,
        // Thêm thông tin agent nếu có
        agentUse: agentInfo ? {
          id: agentInfo.id,
          name: agentInfo.name,
          avatar: agentInfo.avatar
        } : undefined
      };

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting role by ID: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
    }
  }

  /**
   * Lấy danh sách vai trò đã xóa với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách vai trò đã xóa với phân trang
   */
  async getDeletedRoles(
    queryDto: RoleQueryDto,
  ): Promise<PaginatedResult<RoleTrashItemDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'deletedAt',
        sortDirection = 'DESC',
      } = queryDto;

      const result = await this.agentRoleRepository.findDeletedPaginated(
        page,
        limit,
        search,
        sortBy,
        sortDirection,
      );

      // Tạo employee map để lookup nhanh
      const employeeMap = new Map();
      if (result.deletedEmployees) {
        result.deletedEmployees.forEach(emp => {
          employeeMap.set(emp.agentRoleId, emp);
        });
      }

      // Chuyển đổi từ entity sang DTO
      const items = result.items.map((role) => {
        const dto = new RoleTrashItemDto();
        dto.id = role.id;
        dto.name = role.name;
        dto.description = role.description || '';

        // Thông tin người xóa từ employee map
        const employeeInfo = employeeMap.get(role.id);
        if (employeeInfo) {
          dto.deleted = {
            employeeId: employeeInfo.employeeId,
            name: employeeInfo.employeeName,
            avatar: employeeInfo.employeeAvatar,
            date: role.deletedAt || Date.now(),
          };
        }

        return dto;
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error finding deleted roles: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND);
    }
  }

  /**
   * Khôi phục vai trò đã xóa
   * @param id ID của vai trò cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục
   */
  @Transactional()
  async restoreRole(id: string, employeeId: number): Promise<void> {
    try {
      // Kiểm tra vai trò đã xóa có tồn tại không
      const deletedRole = await this.agentRoleRepository.findOne({
        where: { id },
        withDeleted: true,
      });

      if (!deletedRole || !deletedRole.deletedAt) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND, 'Vai trò đã xóa không tồn tại');
      }

      // Khôi phục vai trò
      const restored = await this.agentRoleRepository.restoreAndUpdate(id, employeeId);
      if (!restored) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND, 'Không thể khôi phục vai trò');
      }

      this.logger.debug(`Đã khôi phục vai trò với ID ${id}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error restoring role: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Gán group tool cho vai trò
   * @deprecated Phương thức này không còn được sử dụng vì đã loại bỏ trường groupToolIds
   * @param roleId ID của vai trò
   * @param groupToolIds Danh sách ID của các group tool
   * @param employeeId ID của nhân viên thực hiện hành động
   */
  @Transactional()
  async assignGroupToolsToRole(
    _roleId: string,
    _groupToolIds: number[],
    _employeeId: number,
  ): Promise<void> {
    // Chức năng gán group tool đã bị loại bỏ
    this.logger.log(`Chức năng gán group tool đã bị loại bỏ`);
  }

  /**
   * Lấy thông tin nhân viên từ map
   * @param employeeId ID của nhân viên
   * @param employeeInfoMap Map chứa thông tin nhân viên
   * @param timestamp Thời gian
   * @returns Thông tin nhân viên hoặc null nếu không có
   */
  private getEmployeeInfo(
    employeeId: number | null,
    employeeInfoMap: Map<number, EmployeeInfoDto>,
    timestamp?: number | null,
  ): EmployeeInfoDto | null {
    if (!employeeId || !timestamp) return null;

    const employeeInfo = employeeInfoMap.get(employeeId);
    if (!employeeInfo) return null;

    return {
      employeeId: employeeInfo.employeeId,
      name: employeeInfo.name,
      avatar: employeeInfo.avatar,
      date: timestamp,
    };
  }
}
