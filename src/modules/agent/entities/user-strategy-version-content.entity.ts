import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { StrategyStatusEnum } from '../../strategy/constants/strategy-status.enum';

/**
 * Entity đại diện cho bảng user_strategy_version_contents trong cơ sở dữ liệu
 * Bảng lưu trữ nội dung của các bước trong phiên bản chiến lược của người dùng, bao gồm cả nội dung ẩn (kế thừa) và nội dung có thể chỉnh sửa
 */
@Entity('user_strategy_version_contents')
@Unique('user_strategy_version_content_user_strategy_version_id_step_key', ['strategyVersionId', 'stepOrder'])
export class UserStrategyVersionContent {
  /**
   * ID định danh duy nhất cho mỗi bước nội dung, tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của phiên bản chiến lược do người dùng tạo, tham chiếu đến bảng user_strategy_versions
   */
  @Column({ name: 'strategy_version_id' })
  strategyVersionId: number;

  /**
   * ID của bước nội dung gốc từ phiên bản chính thức, tham chiếu đến bảng strategy_content_steps
   */
  @Column({ name: 'strategy_content_step_id' })
  strategyContentStepId: number;

  /**
   * ID của agent người dùng, tham chiếu đến bảng agents_user
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string;

  /**
   * Nội dung có thể chỉnh sửa của bước, đã được tùy chỉnh bởi người dùng
   */
  @Column({ name: 'editable_example', type: 'text' })
  editableExample: string;

  /**
   * Thứ tự của bước trong chuỗi xử lý, kế thừa từ bước gốc
   */
  @Column({ name: 'step_order' })
  stepOrder: number;

  /**
   * Cờ đánh dấu bước có được chỉnh sửa hay không
   */
  @Column({ name: 'edited', type: 'boolean', default: false })
  edited: boolean;

  /**
   * Trạng thái của hàm: DRAFT, PENDING, APPROVED, REJECTED, DELETE
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: StrategyStatusEnum,
    default: StrategyStatusEnum.DRAFT
  })
  status: StrategyStatusEnum;
}
