import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { ToolsStrategy } from '../entities/tools-strategy.entity';

/**
 * Repository xử lý các thao tác liên quan đến mối quan hệ giữa chiến lược và công cụ
 */
@Injectable()
export class ToolsStrategyRepository extends Repository<ToolsStrategy> {
  constructor(private dataSource: DataSource) {
    super(ToolsStrategy, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản
   * @returns Query builder
   */
  private createBaseQuery(): SelectQueryBuilder<ToolsStrategy> {
    return this.createQueryBuilder('tools_strategy');
  }

  /**
   * Tìm tất cả các công cụ theo ID phiên bản chiến lược
   * @param strategyVersionId ID của phiên bản chiến lượ<PERSON>
   * @returns Danh sách các công cụ
   */
  async findByStrategyVersionId(strategyVersionId: number): Promise<ToolsStrategy[]> {
    return this.createBaseQuery()
      .where('tools_strategy.strategy_version_id = :strategyVersionId', { strategyVersionId })
      .getMany();
  }

  /**
   * Xóa tất cả các liên kết công cụ theo ID phiên bản chiến lược
   * @param strategyVersionId ID của phiên bản chiến lược
   * @returns Kết quả xóa
   */
  async deleteByStrategyVersionId(strategyVersionId: number): Promise<void> {
    await this.createBaseQuery()
      .delete()
      .where('strategy_version_id = :strategyVersionId', { strategyVersionId })
      .execute();
  }

  /**
   * Tạo nhiều liên kết công cụ cùng lúc
   * @param toolsValues Danh sách các giá trị cần tạo
   * @returns Kết quả tạo
   */
  async createBulk(toolsValues: Partial<ToolsStrategy>[]): Promise<void> {
    if (!toolsValues || toolsValues.length === 0) {
      return;
    }
    
    await this.dataSource.createQueryBuilder()
      .insert()
      .into(ToolsStrategy)
      .values(toolsValues)
      .execute();
  }
}
