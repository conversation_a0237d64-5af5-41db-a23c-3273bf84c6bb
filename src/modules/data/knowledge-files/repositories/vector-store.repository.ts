import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { VectorStore } from '../entities/vector-store.entity';
import { PaginatedResult } from '@common/response/api-response-dto';
import { OwnerType } from '@shared/enums';
import { QueryVectorStoreDto } from '../admin/dto';
import { FileHelper } from '../helpers/file.helper';

@Injectable()
export class VectorStoreRepository extends Repository<VectorStore> {
  private readonly logger = new Logger(VectorStoreRepository.name);

  constructor(private dataSource: DataSource) {
    super(VectorStore, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho vector store
   * @returns SelectQueryBuilder<VectorStore>
   */
  private createBaseQuery(): SelectQueryBuilder<VectorStore> {
    return this.createQueryBuilder('vs');
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách vector store của người dùng với phân trang
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách vector store với phân trang
   */
  async findAllByUserIdWithPagination(
    queryDto: QueryVectorStoreDto,
    userId: number,
  ): Promise<PaginatedResult<VectorStore>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'desc',
      } = queryDto;

      // Tạo query builder
      const queryBuilder = this.createBaseQuery()
        .where('vs.owner_id = :userId', { userId })
        .andWhere('vs.owner_type = :ownerType', { ownerType: OwnerType.USER });

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere('vs.name ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Áp dụng sắp xếp
      const sortColumn = this.getSortColumn(sortBy);
      const direction = sortDirection?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
      queryBuilder.orderBy(`vs.${sortColumn}`, direction);

      // Áp dụng phân trang
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Thực hiện truy vấn
      const [items, totalItems] = await queryBuilder.getManyAndCount();

      // Trả về kết quả phân trang
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách vector store: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Chuyển đổi tên trường sắp xếp thành tên cột trong database
   * @param sortBy Tên trường sắp xếp
   * @returns Tên cột trong database
   */
  private getSortColumn(sortBy?: string): string {
    if (!sortBy) return 'created_at';

    switch (sortBy.toLowerCase()) {
      case 'name':
        return 'name';
      case 'storage':
        return 'storage';
      case 'createdat':
      case 'created_at':
      case 'createat':
      default:
        return 'created_at';
    }
  }

  /**
   * Tìm vector store theo ID và người dùng
   * @param id ID của vector store
   * @param userId ID của người dùng
   * @returns Vector store nếu tìm thấy, null nếu không tìm thấy
   */
  async findOneByIdAndUserId(id: string, userId: number): Promise<VectorStore | null> {
    try {
      return this.findOne({
        where: {
          id,
          ownerId: userId,
          ownerType: OwnerType.USER,
        },
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm vector store theo ID và userId: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm vector store theo ID và admin
   * @param id ID của vector store
   * @param employeeId ID của admin
   * @returns Vector store nếu tìm thấy, null nếu không tìm thấy
   */
  async findOneByIdAndEmployeeId(id: string, employeeId: number): Promise<VectorStore | null> {
    try {
      return this.findOne({
        where: {
          id,
          ownerId: employeeId,
          ownerType: OwnerType.ADMIN,
        },
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm vector store theo ID và employeeId: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách vector store với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách vector store với phân trang
   */
  async findAllWithPagination(queryDto: QueryVectorStoreDto): Promise<PaginatedResult<VectorStore>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'desc',
        ownerType,
      } = queryDto;

      // Tạo query builder
      const queryBuilder = this.createBaseQuery();

      // Nếu có lọc theo ownerType, thêm điều kiện
      if (ownerType) {
        queryBuilder.andWhere('vs.owner_type = :ownerType', { ownerType });
      }

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere('vs.name ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Áp dụng sắp xếp
      const sortColumn = FileHelper.getSortColumn(sortBy);
      const direction = sortDirection?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
      queryBuilder.orderBy(`vs.${sortColumn}`, direction);

      // Áp dụng phân trang
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Thực hiện truy vấn
      const [items, totalItems] = await queryBuilder.getManyAndCount();

      // Trả về kết quả phân trang
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách vector store: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật dung lượng của vector store
   * @param vectorStoreIds Danh sách ID của các vector store
   * @param storageChange Sự thay đổi về dung lượng (số âm nếu giảm)
   * @returns Kết quả cập nhật
   */
  async updateStorageByIds(vectorStoreIds: string[], storageChange: number): Promise<{ affected: number }> {
    try {
      if (!vectorStoreIds.length) return { affected: 0 };

      // Tạo query builder để cập nhật dung lượng
      const result = await this.createQueryBuilder()
        .update(VectorStore)
        .set({
          storage: () => `storage + ${storageChange}`
        })
        .where('id IN (:...vectorStoreIds)', { vectorStoreIds })
        .execute();

      return { affected: result.affected || 0 };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật dung lượng vector store: ${error.message}`, error.stack);
      return { affected: 0 };
    }
  }

  /**
   * Đếm số lượng file trong vector store
   * @param vectorStoreId ID của vector store
   * @returns Số lượng file trong vector store
   */
  async countFilesByVectorStoreId(vectorStoreId: string): Promise<number> {
    try {
      const result = await this.dataSource
        .createQueryBuilder()
        .select('COUNT(file_id)', 'count')
        .from('vector_store_files', 'vsf')
        .where('vsf.vector_store_id = :vectorStoreId', { vectorStoreId })
        .getRawOne();

      return parseInt(result?.count || '0');
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng file trong vector store: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Đếm số lượng agent sử dụng vector store
   * @param vectorStoreId ID của vector store
   * @returns Số lượng agent sử dụng vector store
   */
  async countAgentsByVectorStoreId(vectorStoreId: string): Promise<number> {
    try {
      const result = await this.dataSource
        .createQueryBuilder()
        .select('COUNT(id)', 'count')
        .from('agents', 'a')
        .where('a.vector_store_id = :vectorStoreId', { vectorStoreId })
        .getRawOne();

      return parseInt(result?.count || '0');
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng agent sử dụng vector store: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Lấy số lượng file trong nhiều vector store
   * @param vectorStoreIds Danh sách ID của các vector store
   * @returns Map với key là ID của vector store và value là số lượng file
   */
  async countFilesInMultipleVectorStores(vectorStoreIds: string[]): Promise<Map<string, number>> {
    try {
      if (vectorStoreIds.length === 0) {
        return new Map<string, number>();
      }

      const fileCounts = await this.dataSource
        .createQueryBuilder()
        .select('vector_store_id', 'storeId')
        .addSelect('COUNT(file_id)', 'fileCount')
        .from('vector_store_files', 'vsf')
        .where('vsf.vector_store_id IN (:...vectorStoreIds)', { vectorStoreIds })
        .groupBy('vector_store_id')
        .getRawMany();

      const fileCountMap = new Map<string, number>();
      fileCounts.forEach((item) => {
        fileCountMap.set(item.storeId, parseInt(item.fileCount));
      });

      return fileCountMap;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng file trong nhiều vector store: ${error.message}`, error.stack);
      return new Map<string, number>();
    }
  }

  /**
   * Lấy số lượng agent sử dụng nhiều vector store
   * @param vectorStoreIds Danh sách ID của các vector store
   * @returns Map với key là ID của vector store và value là số lượng agent
   */
  async countAgentsInMultipleVectorStores(vectorStoreIds: string[]): Promise<Map<string, number>> {
    try {
      if (vectorStoreIds.length === 0) {
        return new Map<string, number>();
      }

      const agentCounts = await this.dataSource
        .createQueryBuilder()
        .select('vector_store_id', 'storeId')
        .addSelect('COUNT(id)', 'agentCount')
        .from('agents', 'a')
        .where('a.vector_store_id IN (:...vectorStoreIds)', { vectorStoreIds })
        .groupBy('vector_store_id')
        .getRawMany();

      const agentCountMap = new Map<string, number>();
      agentCounts.forEach((item) => {
        agentCountMap.set(item.storeId, parseInt(item.agentCount));
      });

      return agentCountMap;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng agent sử dụng nhiều vector store: ${error.message}`, error.stack);
      return new Map<string, number>();
    }
  }

  /**
   * Tính tổng dung lượng vector stores của một người dùng
   * @param userId ID của người dùng
   * @returns Tổng dung lượng (bytes) và số lượng vector stores
   */
  async getTotalStorageByUser(userId: number): Promise<{ totalSize: number; storeCount: number }> {
    try {
      const result = await this.createQueryBuilder('vs')
        .select('SUM(vs.storage)', 'totalSize')
        .addSelect('COUNT(vs.id)', 'storeCount')
        .where('vs.owned_by = :userId', { userId })
        .andWhere('vs.owner_type = :ownerType', { ownerType: OwnerType.USER })
        .getRawOne();

      return {
        totalSize: parseInt(result?.totalSize || '0'),
        storeCount: parseInt(result?.storeCount || '0'),
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tính tổng dung lượng vector stores của user ${userId}: ${error.message}`, error.stack);
      return { totalSize: 0, storeCount: 0 };
    }
  }
}
