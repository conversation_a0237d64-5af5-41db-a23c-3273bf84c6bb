import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { AssignFilesDto } from '../../dto/assign-files.dto';

describe('AssignFilesDto', () => {
  it('nên xác thực DTO hợp lệ với danh sách fileIds', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'b2c3d4e5-f6a7-8901-bcde-f01234567890'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một fileId', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi xác thực với fileIds không phải là mảng', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('nên thất bại khi xác thực với mảng fileIds rỗng', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: [],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('arrayMinSize');
  });

  it('nên thất bại khi xác thực khi thiếu fileIds', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
  });

  it('nên thất bại khi xác thực với fileIds chứa phần tử không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', 123],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi xác thực với fileIds chứa chuỗi rỗng', async () => {
    // Arrange
    const dto = plainToInstance(AssignFilesDto, {
      fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', ''],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  describe('Chunk Settings Validation', () => {
    it('nên hợp lệ với giá trị mặc định cho chunkSize và chunkOverlap', async () => {
      // Arrange
      const dto = plainToInstance(AssignFilesDto, {
        fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
      expect(dto.chunkSize).toBe(2000);
      expect(dto.chunkOverlap).toBe(100);
    });

    it('nên hợp lệ với chunkSize và chunkOverlap tùy chỉnh', async () => {
      // Arrange
      const dto = plainToInstance(AssignFilesDto, {
        fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
        chunkSize: 4000,
        chunkOverlap: 200,
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
      expect(dto.chunkSize).toBe(4000);
      expect(dto.chunkOverlap).toBe(200);
    });

    it('nên hợp lệ với chunkOverlap = 0', async () => {
      // Arrange
      const dto = plainToInstance(AssignFilesDto, {
        fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
        chunkSize: 1000,
        chunkOverlap: 0,
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
      expect(dto.chunkOverlap).toBe(0);
    });

    it('nên thất bại khi chunkSize quá nhỏ', async () => {
      // Arrange
      const dto = plainToInstance(AssignFilesDto, {
        fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
        chunkSize: 50,
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('chunkSize');
      expect(errors[0].constraints).toHaveProperty('min');
    });

    it('nên thất bại khi chunkSize quá lớn', async () => {
      // Arrange
      const dto = plainToInstance(AssignFilesDto, {
        fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
        chunkSize: 10000,
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('chunkSize');
      expect(errors[0].constraints).toHaveProperty('max');
    });

    it('nên thất bại khi chunkOverlap âm', async () => {
      // Arrange
      const dto = plainToInstance(AssignFilesDto, {
        fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
        chunkOverlap: -10,
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('chunkOverlap');
      expect(errors[0].constraints).toHaveProperty('min');
    });

    it('nên thất bại khi chunkOverlap quá lớn', async () => {
      // Arrange
      const dto = plainToInstance(AssignFilesDto, {
        fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
        chunkOverlap: 1500,
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('chunkOverlap');
      expect(errors[0].constraints).toHaveProperty('max');
    });

    it('nên chuyển đổi string thành number cho chunkSize và chunkOverlap', async () => {
      // Arrange
      const dto = plainToInstance(AssignFilesDto, {
        fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890'],
        chunkSize: '3000',
        chunkOverlap: '150',
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
      expect(dto.chunkSize).toBe(3000);
      expect(dto.chunkOverlap).toBe(150);
      expect(typeof dto.chunkSize).toBe('number');
      expect(typeof dto.chunkOverlap).toBe('number');
    });
  });
});
