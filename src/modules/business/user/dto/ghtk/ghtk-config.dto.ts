import { IsString, IsOptional, IsBoolean, IsNumber, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho cấu hình GHTK
 */
export class GHTKConfigDto {
  @ApiProperty({
    description: 'Token API của GHTK',
    example: 'APITokenSample-ca441e70288cB0515F310742'
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'Mã đối tác/cửa hàng do GHTK cung cấp',
    example: 'PARTNER_CODE_SAMPLE',
    required: false
  })
  @IsOptional()
  @IsString()
  partnerCode?: string;

  @ApiProperty({
    description: 'Sử dụng môi trường test hay production',
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  isTestMode?: boolean = true;
}

/**
 * DTO cho thông tin sản phẩm trong đơn hàng GHTK
 */
export class GHTKProductDto {
  @ApiProperty({
    description: 'Tên hàng hóa',
    example: 'Laptop Asus'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Giá trị của hàng hóa (VNĐ)',
    example: 8000000,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Giá sản phẩm phải là số' })
  price?: number;

  @ApiProperty({
    description: 'Cân nặng hàng hóa (kg)',
    example: 2.5
  })
  @IsNumber({}, { message: 'Cân nặng phải là số' })
  weight: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm',
    example: 1,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số lượng sản phẩm phải là số' })
  quantity?: number;

  @ApiProperty({
    description: 'Mã sản phẩm do shop hoặc GHTK cấp',
    example: '23304A3MHLMVMXX625',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Mã sản phẩm phải là chuỗi' })
  productCode?: string;
}

/**
 * DTO cho thông tin đơn hàng GHTK
 */
export class GHTKOrderDto {
  @ApiProperty({
    description: 'Mã đơn hàng bên đối tác',
    example: 'ORDER_123456'
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Tên người lấy hàng',
    example: 'Kho HCM nội thành'
  })
  @IsString()
  pickName: string;

  @ApiProperty({
    description: 'Địa chỉ lấy hàng',
    example: '590 CMT8 P.11'
  })
  @IsString()
  pickAddress: string;

  @ApiProperty({
    description: 'Tỉnh/thành lấy hàng',
    example: 'TP. Hồ Chí Minh'
  })
  @IsString()
  pickProvince: string;

  @ApiProperty({
    description: 'Quận/huyện lấy hàng',
    example: 'Quận 3'
  })
  @IsString()
  pickDistrict: string;

  @ApiProperty({
    description: 'Phường/xã lấy hàng',
    example: 'Phường 1'
  })
  @IsString()
  pickWard: string;

  @ApiProperty({
    description: 'Số điện thoại người lấy hàng',
    example: '0911222333'
  })
  @IsString()
  pickTel: string;

  @ApiProperty({
    description: 'Tên người nhận hàng',
    example: 'Nguyễn Văn A'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Địa chỉ nhận hàng',
    example: '123 Nguyễn Chí Thanh'
  })
  @IsString()
  address: string;

  @ApiProperty({
    description: 'Tỉnh/thành nhận hàng',
    example: 'TP. Hồ Chí Minh'
  })
  @IsString()
  province: string;

  @ApiProperty({
    description: 'Quận/huyện nhận hàng',
    example: 'Quận 1'
  })
  @IsString()
  district: string;

  @ApiProperty({
    description: 'Phường/xã nhận hàng',
    example: 'Phường Bến Nghé'
  })
  @IsString()
  ward: string;

  @ApiProperty({
    description: 'Xã/thôn nhận hàng',
    example: 'Khác',
    required: false
  })
  @IsOptional()
  @IsString()
  hamlet?: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0987654321'
  })
  @IsString()
  tel: string;

  @ApiProperty({
    description: 'Đơn freeship (1: freeship, 0: tính phí)',
    example: '0',
    required: false
  })
  @IsOptional()
  @IsString()
  isFreeship?: string;

  @ApiProperty({
    description: 'Ngày hẹn lấy hàng (YYYY-MM-DD)',
    example: '2024-01-15',
    required: false
  })
  @IsOptional()
  @IsString()
  pickDate?: string;

  @ApiProperty({
    description: 'Số tiền thu hộ COD (VNĐ)',
    example: 47000,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số tiền COD phải là số' })
  pickMoney?: number;

  @ApiProperty({
    description: 'Ghi chú đơn hàng',
    example: 'Khối lượng tính cước tối đa: 1.00 kg',
    required: false
  })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    description: 'Giá trị tính khai giá của đơn (VNĐ)',
    example: 3000000
  })
  @IsNumber({}, { message: 'Giá trị đơn hàng phải là số' })
  value: number;

  @ApiProperty({
    description: 'Phương thức vận chuyển (fly: đường bay, road: đường bộ)',
    example: 'road',
    required: false
  })
  @IsOptional()
  @IsString()
  transport?: string;

  @ApiProperty({
    description: 'Cách lấy hàng (cod: thu hộ, post: đối soát)',
    example: 'cod',
    required: false
  })
  @IsOptional()
  @IsString()
  pickOption?: string;

  @ApiProperty({
    description: 'Dịch vụ xFast/xTeam (xteam hoặc để trống)',
    example: 'xteam',
    required: false
  })
  @IsOptional()
  @IsString()
  deliverOption?: string;

  @ApiProperty({
    description: 'Mã nhãn gán cho đơn',
    example: [1, 2],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  tags?: number[];

  @ApiProperty({
    description: 'Chi tiết nhãn phụ',
    example: [1, 2],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'SubTags phải là mảng' })
  subTags?: number[];
}
