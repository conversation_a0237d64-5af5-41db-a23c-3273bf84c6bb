import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  IsEnum,
} from 'class-validator';
import { CustomFieldTypeEnum } from '@modules/business/enums';

/**
 * DTO cho cập nhật trường tùy chỉnh
 */
export class UpdateCustomFieldDto {

  /**
   * Nhãn hiển thị
   * @example "Họ và tên đầy đủ"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Họ và tên đầy đủ',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nhãn hiển thị phải là chuỗi' })
  @MaxLength(255, { message: 'Nhãn hiển thị không được vượt quá 255 ký tự' })
  label?: string;

  /**
   * Loại trường
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
    required: false,
    enum: CustomFieldTypeEnum,
    enumName: 'CustomFieldTypeEnum',
  })
  @IsOptional()
  @IsEnum(CustomFieldTypeEnum, {
    message: `Loại trường phải là một trong các giá trị: ${Object.values(CustomFieldTypeEnum).join(', ')}`
  })
  type?: CustomFieldTypeEnum;

  /**
   * Trường bắt buộc hay không
   * @example true
   */
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường bắt buộc phải là boolean' })
  required?: boolean;

  /**
   * Cấu hình JSON
   * @example { "validation": { "minLength": 5, "maxLength": 60, "pattern": "^[a-zA-Z0-9 ]*$" }, "placeholder": "Nhập họ và tên đầy đủ", "variant": "outlined", "size": "small" }
   */
  @ApiProperty({
    description: 'Cấu hình JSON',
    example: {
      validation: { minLength: 5, maxLength: 60, pattern: '^[a-zA-Z0-9 ]*$' },
      placeholder: 'Nhập họ và tên đầy đủ',
      variant: 'outlined',
      size: 'small',
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình JSON phải là đối tượng' })
  configJson?: any;
}
