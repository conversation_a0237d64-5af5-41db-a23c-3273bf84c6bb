import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsObject, IsString, Matches, IsEnum } from 'class-validator';
import { CustomFieldTypeEnum } from '@modules/business/enums';

/**
 * DTO cho việc tạo trường tùy chỉnh mới
 */
export class CreateCustomFieldDto {

  @ApiProperty({
    description: 'ID cấu hình (phải là unique)',
    example: 'product_color',
  })
  @IsNotEmpty()
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Màu sắc',
  })
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
    enum: CustomFieldTypeEnum,
    enumName: 'CustomFieldTypeEnum',
  })
  @IsNotEmpty()
  @IsEnum(CustomFieldTypeEnum, {
    message: `<PERSON><PERSON><PERSON> trường phải là một trong các giá trị: ${Object.values(CustomFieldTypeEnum).join(', ')}`
  })
  type: CustomFieldTypeEnum;

  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  required: boolean;

  @ApiProperty({
    description: 'Cấu hình chi tiết',
    example: {
      placeholder: 'Nhập màu sắc',
      maxLength: 50,
      description: 'Màu sắc chính của sản phẩm',
    },
  })
  @IsNotEmpty()
  @IsObject()
  configJson: any;
}
