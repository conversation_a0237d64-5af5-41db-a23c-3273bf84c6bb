import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng model_fine_tune trong cơ sở dữ liệu
 * Lưu các mô hình fine-tune (tùy chỉnh lại từ base model)
 */
@Entity('model_fine_tune')
export class ModelFineTune {
  /**
   * UUID của model fine-tune
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
 * Tên hiển thị của fine-tuned model
 */
  @Column({
    name: 'base_model_name',
    type: 'varchar',
    length: 255,
    comment: 'Tên hiển thị của base model'
  })
  baseModelName: string;

  /**
   * ID của user sở hữu model
   */
  @Column({
    type: 'integer',
    name: 'user_id',
    comment: 'ID của user sở hữu model'
  })
  userId: number;

  /**
   * ID của base model được fine-tune
   */
  @Column({
    type: 'uuid',
    name: 'base_model_id',
    comment: 'ID của base model được fine-tune'
  })
  baseModelId: string;

  /**
   * UUID của model registry capabilities
   */
  @Column({
    type: 'uuid',
    name: 'capabilities',
    comment: 'UUID của model registry capabilities'
  })
  capabilities: string;

  /**
   * ID của system key LLM được sử dụng
   */
  @Column({
    type: 'uuid',
    name: 'key_llm_id',
    comment: 'ID của user key LLM được sử dụng'
  })
  keyLlmId: string;

  /**
   * ID của fine-tune history
   */
  @Column({
    name: 'history_id',
    type: 'uuid',
    comment: 'Liến kết đến fine_tune_histories'
  })
  historyId: string;

  /**
   * Thời điểm tạo
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm xóa mềm
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;
}
