// Mock cho ConfigModule
export const mockConfigService = {
  get: jest.fn((key: string) => {
    // Trả về các giá trị mock cho các khóa cấu hình
    const configValues: { [key: string]: any } = {
      // Database
      DB_HOST: 'localhost',
      DB_PORT: 5432,
      DB_USERNAME: 'test',
      DB_PASSWORD: 'test',
      DB_DATABASE: 'test_db',
      
      // Cloudflare R2
      CF_R2_ACCESS_KEY: 'mock_access_key',
      CF_R2_SECRET_KEY: 'mock_secret_key',
      CF_R2_ENDPOINT: 'https://mock-endpoint.com',
      CF_BUCKET_NAME: 'mock-bucket',
      
      // OpenAI
      OPENAI_API_KEY: 'mock_openai_key',
      
      // CDN
      CDN_URL: 'https://mock-cdn.com',
      CDN_SECRET_KEY: 'mock_cdn_key',
      
      // JWT
      JWT_SECRET: 'mock_jwt_secret',
      JWT_EXPIRES_IN: '1h',
      JWT_REFRESH_EXPIRES_IN: '7d',
    };
    
    return configValues[key];
  }),
};

export const mockConfigModule = {
  provide: 'ConfigService',
  useValue: mockConfigService,
};
