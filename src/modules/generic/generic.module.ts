import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GenericAdminModule } from './admin/generic-admin.module';
import { GenericUserModule } from './user/generic-user.module';
import { GenericPage, GenericPageSubmission, GenericPageTemplate, GenericPageTemplateTag } from './entities';
import { GenericPageRepository, GenericPageSubmissionRepository, GenericPageTemplateRepository, GenericPageTemplateTagRepository } from './repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GenericPage,
      GenericPageTemplate,
      GenericPageTemplateTag,
      GenericPageSubmission,
    ]),
    GenericAdminModule,
    GenericUserModule,
  ],
  providers: [
    GenericPageRepository,
    GenericPageTemplateRepository,
    GenericPageTemplateTagRepository,
    GenericPageSubmissionRepository,
  ],
  exports: [
    GenericAdminModule,
    GenericUserModule,
    TypeOrmModule,
  ],
})
export class GenericModule {}
