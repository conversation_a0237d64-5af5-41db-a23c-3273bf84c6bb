import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@/common';
import { GENERIC_PAGE_ERROR_CODES } from '../../exceptions/generic-page-error.code';
import { GenericPageRepository } from '../../repositories/generic-page.repository';
import { GenericPageSubmissionRepository } from '../../repositories/generic-page-submission.repository';
import { GenericPageSubmission } from '../../entities/generic-page-submission.entity';
import { GenericPageStatusEnum } from '../../constants/generic-page.enum';
import { GenericPageResponseDto, SubmissionResponseDto, SubmitFormDto } from '../dto';

@Injectable()
export class GenericPageUserService {
  private readonly logger = new Logger(GenericPageUserService.name);

  constructor(
    private readonly genericPageRepository: GenericPageRepository,
    private readonly genericPageSubmissionRepository: GenericPageSubmissionRepository,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> thông tin trang đã xuất bản theo đường dẫn
   * @param path Đường dẫn của trang
   * @returns Thông tin trang
   */
  async getPublishedGenericPageByPath(path: string): Promise<GenericPageResponseDto> {
    try {
      const genericPage = await this.genericPageRepository.findPublishedByPath(path);
      return this.mapToResponseDto(genericPage);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting published generic page by path: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
        `Không tìm thấy trang với đường dẫn ${path}`,
      );
    }
  }

  /**
   * Gửi dữ liệu form
   * @param pageId ID của trang
   * @param submitFormDto Dữ liệu form
   * @param userId ID của người dùng (nếu đã đăng nhập)
   * @param ipAddress Địa chỉ IP của người gửi
   * @param userAgent User agent của người gửi
   * @returns Thông tin về dữ liệu đã gửi
   */
  async submitForm(
    pageId: string,
    submitFormDto: SubmitFormDto,
    userId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<SubmissionResponseDto> {
    try {
      // Tìm trang theo ID
      const genericPage = await this.genericPageRepository.findById(pageId);

      // Kiểm tra xem trang đã xuất bản chưa
      if (genericPage.status !== GenericPageStatusEnum.PUBLISHED) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_PUBLISHED,
          'Trang chưa được xuất bản',
        );
      }

      // Tạo entity mới
      const submission = new GenericPageSubmission();
      submission.pageId = pageId;
      submission.data = submitFormDto.data;
      submission.createdAt = Date.now();
      submission.updatedAt = Date.now();
      submission.ipAddress = ipAddress || '';
      submission.userAgent = userAgent || '';
      submission.userId = userId || '';

      // Lưu vào cơ sở dữ liệu
      const savedSubmission = await this.genericPageSubmissionRepository.save(submission);

      // Chuyển đổi sang DTO
      return this.mapToSubmissionResponseDto(savedSubmission);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error submitting form: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_CREATE_ERROR,
        'Lỗi khi gửi dữ liệu form',
      );
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   * @param genericPage Entity GenericPage
   * @returns DTO GenericPageResponseDto
   */
  private mapToResponseDto(genericPage: any): GenericPageResponseDto {
    const responseDto = new GenericPageResponseDto();
    responseDto.id = genericPage.id;
    responseDto.name = genericPage.name;
    responseDto.description = genericPage.description;
    responseDto.path = genericPage.path;
    responseDto.config = genericPage.config;
    responseDto.publishedAt = genericPage.publishedAt;
    return responseDto;
  }

  /**
   * Chuyển đổi entity sang DTO
   * @param submission Entity GenericPageSubmission
   * @returns DTO SubmissionResponseDto
   */
  private mapToSubmissionResponseDto(submission: GenericPageSubmission): SubmissionResponseDto {
    const responseDto = new SubmissionResponseDto();
    responseDto.id = submission.id;
    responseDto.pageId = submission.pageId;
    responseDto.status = submission.status;
    responseDto.createdAt = submission.createdAt;
    responseDto.message = 'Dữ liệu đã được gửi thành công';
    return responseDto;
  }
}
